# ESClientRHL 完整使用文档

## 目录
1. [项目简介](#项目简介)
2. [快速开始](#快速开始)
3. [核心概念](#核心概念)
4. [API参考](#api参考)
5. [配置说明](#配置说明)
6. [示例代码](#示例代码)
7. [最佳实践](#最佳实践)
8. [故障排除](#故障排除)

## 项目简介

ESClientRHL 是一个基于 Elasticsearch 7.x 的 Java 客户端工具库，提供了简化的 API 来操作 Elasticsearch。

### 主要特性

- **简化的API设计**：提供直观易用的方法来操作 Elasticsearch
- **配置驱动**：支持通过 EntityConfig 进行灵活配置，替代注解方式
- **类型安全**：完整的泛型支持，避免类型转换错误
- **功能完整**：支持索引管理、文档CRUD、复杂查询、聚合等所有ES功能
- **Spring集成**：无缝集成 Spring Boot 应用
- **纯API模式**：支持不依赖Spring的纯API调用方式

### 版本要求

- Java 8+
- Elasticsearch 7.3.1+
- Spring Framework 5.1.3+ (可选)
- Spring Boot 2.0.5+ (可选)

## 快速开始

### 1. 添加依赖

```xml
<dependency>
    <groupId>cn.zxporz</groupId>
    <artifactId>esclientrhl</artifactId>
    <version>7.0.2</version>
</dependency>
```

### 2. 基本使用

#### 方式一：使用 ESClientFactory（推荐）

```java
// 创建客户端工厂
ESClientFactory factory = new ESClientFactory("127.0.0.1:9200");

// 获取操作模板
ElasticsearchTemplate<User, String> template = factory.getElasticsearchTemplate();

// 获取索引管理器
ElasticsearchIndex<User> indexManager = factory.getElasticsearchIndex();

// 使用完毕后关闭
factory.close();
```

#### 方式二：Spring Boot 集成

```java
@SpringBootApplication
@EnableESTools
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}

@Service
public class UserService {
    @Autowired
    private ElasticsearchTemplate<User, String> elasticsearchTemplate;
    
    // 业务方法...
}
```

### 3. 创建实体类

```java
public class User {
    private String id;
    private String name;
    private Integer age;
    private String email;
    private Date createTime;
    
    // 构造函数、getter、setter...
}
```

### 4. 配置实体映射

```java
// 创建元数据配置
MetaDataConfig metaData = new MetaDataConfig("user_index")
    .setNumberOfShards(1)
    .setNumberOfReplicas(0)
    .setPrintLog(true);

// 创建实体配置
EntityConfig userConfig = new EntityConfig(User.class)
    .setMetaData(metaData)
    .setIdField("id")
    .addFieldMapping("name", new MappingConfig()
        .setDatatype(DataType.text_type)
        .setAnalyzer(Analyzer.standard)
        .setKeyword(true))
    .addFieldMapping("age", new MappingConfig()
        .setDatatype(DataType.integer_type))
    .addFieldMapping("email", new MappingConfig()
        .setDatatype(DataType.keyword_type));
```

### 5. 基本操作

```java
// 创建索引
indexManager.createIndex(userConfig);

// 保存文档
User user = new User();
user.setId("001");
user.setName("张三");
user.setAge(25);
template.save(user, userConfig);

// 查询文档
User retrieved = template.getById("001", userConfig);

// 搜索文档
List<User> users = template.search(QueryBuilders.matchAllQuery(), userConfig);

// 删除文档
template.delete("001", userConfig);
```

## 核心概念

### 1. ESClientFactory

工厂类，负责创建和管理 Elasticsearch 客户端及相关组件。

**主要方法：**
- `getElasticsearchTemplate()`: 获取数据操作模板
- `getElasticsearchIndex()`: 获取索引管理器
- `getIndexTools()`: 获取索引工具
- `getClient()`: 获取原生ES客户端

### 2. EntityConfig

实体配置类，替代注解方式配置ES映射。

**主要组成：**
- `MetaDataConfig`: 索引元数据配置
- `MappingConfig`: 字段映射配置
- `idField`: ID字段名称
- `scoreField`: 评分字段名称

### 3. ElasticsearchTemplate

数据操作模板，提供所有ES数据操作方法。

**主要功能：**
- 文档CRUD操作
- 复杂查询和聚合
- 批量操作
- 分页查询

### 4. ElasticsearchIndex

索引管理器，负责索引的创建、删除、存在性检查等。

## API参考

### ElasticsearchTemplate 主要方法

#### 基础CRUD操作

```java
// 保存文档
boolean save(T entity, EntityConfig config)

// 根据ID查询
T getById(M id, EntityConfig config)

// 更新文档
boolean update(T entity, EntityConfig config)

// 删除文档
boolean delete(M id, EntityConfig config)
```

#### 查询操作

```java
// 基础搜索
List<T> search(QueryBuilder queryBuilder, EntityConfig config)

// 分页搜索
PageList<T> search(QueryBuilder queryBuilder, PageSortHighLight pageSort, EntityConfig config)

// 计数查询
long count(QueryBuilder queryBuilder, EntityConfig config)
```

#### 批量操作

```java
// 批量保存（原生方法，需要注解配置）
BulkResponse save(List<T> list)

// 批量更新
BulkResponse bulkUpdate(List<T> list)
```

### ElasticsearchIndex 主要方法

```java
// 创建索引
boolean createIndex(EntityConfig config)

// 删除索引
boolean deleteIndex(EntityConfig config)

// 检查索引是否存在
boolean exists(EntityConfig config)
```

### 查询构建器示例

```java
// 匹配所有
QueryBuilders.matchAllQuery()

// 词条查询
QueryBuilders.termQuery("field", "value")

// 范围查询
QueryBuilders.rangeQuery("age").gte(18).lte(65)

// 文本匹配
QueryBuilders.matchQuery("name", "张三")

// 布尔查询
QueryBuilders.boolQuery()
    .must(QueryBuilders.termQuery("status", "active"))
    .filter(QueryBuilders.rangeQuery("age").gte(18))
```

## 配置说明

### MetaDataConfig 配置项

```java
MetaDataConfig config = new MetaDataConfig("index_name")
    .setNumberOfShards(1)           // 分片数量
    .setNumberOfReplicas(0)         // 副本数量
    .setPrintLog(true)              // 是否打印日志
    .setAutoCreateIndex(true)       // 是否自动创建索引
    .setAlias(false)                // 是否使用别名
    .setRollover(false)             // 是否启用滚动索引
    .setMaxResultWindow(10000);     // 最大结果窗口
```

### MappingConfig 配置项

```java
MappingConfig mapping = new MappingConfig()
    .setDatatype(DataType.text_type)           // 数据类型
    .setAnalyzer(Analyzer.ik_max_word)         // 分析器
    .setKeyword(true)                          // 是否支持keyword
    .setNgram(true)                            // 是否支持ngram
    .setDateFormat(Arrays.asList("yyyy-MM-dd")) // 日期格式
    .setNormalizer("lowercase")                // 标准化器
    .setNullValue("NULL");                     // 空值处理
```

### 数据类型支持

- `DataType.text_type`: 文本类型
- `DataType.keyword_type`: 关键字类型
- `DataType.integer_type`: 整数类型
- `DataType.long_type`: 长整数类型
- `DataType.double_type`: 双精度类型
- `DataType.boolean_type`: 布尔类型
- `DataType.date_type`: 日期类型
- `DataType.geo_point_type`: 地理位置类型
- `DataType.nested_type`: 嵌套类型

### 分析器支持

- `Analyzer.standard`: 标准分析器
- `Analyzer.ik_max_word`: IK最大词分析器
- `Analyzer.ik_smart`: IK智能分析器
- `Analyzer.keyword`: 关键字分析器

## 示例代码

### 完整的用户管理示例

```java
public class UserManager {
    private ESClientFactory factory;
    private ElasticsearchTemplate<User, String> template;
    private ElasticsearchIndex<User> indexManager;
    private EntityConfig userConfig;
    
    public UserManager() {
        // 初始化工厂
        factory = new ESClientFactory("127.0.0.1:9200");
        template = factory.getElasticsearchTemplate();
        indexManager = factory.getElasticsearchIndex();
        
        // 创建配置
        userConfig = createUserConfig();
        
        // 创建索引
        try {
            if (!indexManager.exists(userConfig)) {
                indexManager.createIndex(userConfig);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to create index", e);
        }
    }
    
    private EntityConfig createUserConfig() {
        MetaDataConfig metaData = new MetaDataConfig("user_index")
            .setNumberOfShards(1)
            .setNumberOfReplicas(0)
            .setPrintLog(true);
        
        return new EntityConfig(User.class)
            .setMetaData(metaData)
            .setIdField("id")
            .addFieldMapping("name", new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.standard)
                .setKeyword(true))
            .addFieldMapping("age", new MappingConfig()
                .setDatatype(DataType.integer_type))
            .addFieldMapping("email", new MappingConfig()
                .setDatatype(DataType.keyword_type))
            .addFieldMapping("createTime", new MappingConfig()
                .setDatatype(DataType.date_type));
    }
    
    public void saveUser(User user) throws Exception {
        template.save(user, userConfig);
    }
    
    public User getUser(String id) throws Exception {
        return template.getById(id, userConfig);
    }
    
    public List<User> searchUsers(String name) throws Exception {
        return template.search(
            QueryBuilders.matchQuery("name", name), userConfig);
    }
    
    public List<User> getUsersByAgeRange(int minAge, int maxAge) throws Exception {
        return template.search(
            QueryBuilders.rangeQuery("age").gte(minAge).lte(maxAge), 
            userConfig);
    }
    
    public PageList<User> getUsersWithPagination(int page, int size) throws Exception {
        PageSortHighLight pageSort = new PageSortHighLight(page, size);
        return template.search(QueryBuilders.matchAllQuery(), pageSort, userConfig);
    }
    
    public void updateUser(User user) throws Exception {
        template.update(user, userConfig);
    }
    
    public void deleteUser(String id) throws Exception {
        template.delete(id, userConfig);
    }
    
    public long countUsers() throws Exception {
        return template.count(QueryBuilders.matchAllQuery(), userConfig);
    }
    
    public void close() {
        if (factory != null) {
            factory.close();
        }
    }
}
```

### 复杂查询示例

```java
public class AdvancedSearchExample {

    public List<User> complexSearch(ElasticsearchTemplate<User, String> template,
                                   EntityConfig config) throws Exception {

        // 构建复杂的布尔查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
            // 必须匹配：年龄在18-65之间
            .must(QueryBuilders.rangeQuery("age").gte(18).lte(65))
            // 必须匹配：状态为活跃
            .must(QueryBuilders.termQuery("status", "ACTIVE"))
            // 应该匹配：名字包含"张"或"李"
            .should(QueryBuilders.matchQuery("name", "张"))
            .should(QueryBuilders.matchQuery("name", "李"))
            // 最少匹配一个should条件
            .minimumShouldMatch(1)
            // 不能匹配：邮箱包含"test"
            .mustNot(QueryBuilders.wildcardQuery("email", "*test*"));

        return template.search(boolQuery, config);
    }

    public PageList<User> searchWithHighlight(ElasticsearchTemplate<User, String> template,
                                             EntityConfig config, String keyword) throws Exception {

        // 创建分页和高亮配置
        PageSortHighLight pageSort = new PageSortHighLight(1, 10);

        // 添加排序
        pageSort.setSort("age", SortOrder.ASC);
        pageSort.setSort("createTime", SortOrder.DESC);

        // 添加高亮
        pageSort.setHighLight("name");
        pageSort.setHighLight("description");

        // 执行搜索
        QueryBuilder query = QueryBuilders.multiMatchQuery(keyword, "name", "description");
        return template.search(query, pageSort, config);
    }
}
```

### 聚合查询示例

```java
public class AggregationExample {

    public void performAggregations(ElasticsearchTemplate<User, String> template,
                                   EntityConfig config) throws Exception {

        // 基础计数
        long totalUsers = template.count(QueryBuilders.matchAllQuery(), config);
        System.out.println("总用户数: " + totalUsers);

        // 按条件计数
        long activeUsers = template.count(
            QueryBuilders.termQuery("status", "ACTIVE"), config);
        System.out.println("活跃用户数: " + activeUsers);

        // 年龄范围统计
        long youngUsers = template.count(
            QueryBuilders.rangeQuery("age").lt(30), config);
        System.out.println("年轻用户数(< 30岁): " + youngUsers);

        long middleAgedUsers = template.count(
            QueryBuilders.rangeQuery("age").gte(30).lt(50), config);
        System.out.println("中年用户数(30-50岁): " + middleAgedUsers);

        long seniorUsers = template.count(
            QueryBuilders.rangeQuery("age").gte(50), config);
        System.out.println("老年用户数(>= 50岁): " + seniorUsers);
    }
}
```

## 最佳实践

### 1. 连接管理

```java
// 推荐：使用单例模式管理ESClientFactory
@Component
public class ESClientManager {
    private static ESClientFactory factory;

    @PostConstruct
    public void init() {
        factory = new ESClientFactory("127.0.0.1:9200");
    }

    public static ESClientFactory getFactory() {
        return factory;
    }

    @PreDestroy
    public void destroy() {
        if (factory != null) {
            factory.close();
        }
    }
}
```

### 2. 配置管理

```java
// 推荐：将EntityConfig配置集中管理
@Configuration
public class ESConfigManager {

    @Bean
    public EntityConfig userConfig() {
        return new EntityConfig(User.class)
            .setMetaData(new MetaDataConfig("user_index")
                .setNumberOfShards(1)
                .setNumberOfReplicas(0))
            .setIdField("id")
            .addFieldMapping("name", textMapping())
            .addFieldMapping("age", integerMapping());
    }

    private MappingConfig textMapping() {
        return new MappingConfig()
            .setDatatype(DataType.text_type)
            .setAnalyzer(Analyzer.standard)
            .setKeyword(true);
    }

    private MappingConfig integerMapping() {
        return new MappingConfig()
            .setDatatype(DataType.integer_type);
    }
}
```

### 3. 异常处理

```java
public class UserService {

    public User saveUser(User user) {
        try {
            boolean success = template.save(user, userConfig);
            if (!success) {
                throw new ServiceException("保存用户失败");
            }
            return user;
        } catch (Exception e) {
            log.error("保存用户异常: {}", e.getMessage(), e);
            throw new ServiceException("保存用户异常: " + e.getMessage());
        }
    }

    public User getUser(String id) {
        try {
            User user = template.getById(id, userConfig);
            if (user == null) {
                throw new NotFoundException("用户不存在: " + id);
            }
            return user;
        } catch (Exception e) {
            log.error("查询用户异常: {}", e.getMessage(), e);
            throw new ServiceException("查询用户异常: " + e.getMessage());
        }
    }
}
```
