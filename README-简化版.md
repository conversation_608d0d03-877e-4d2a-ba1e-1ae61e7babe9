# ESClientRHL - 简化版使用指南

[![License](https://img.shields.io/badge/license-Apache%202-blue.svg)](https://www.apache.org/licenses/LICENSE-2.0)
[![Java](https://img.shields.io/badge/java-8+-green.svg)](https://www.oracle.com/java/)
[![Elasticsearch](https://img.shields.io/badge/elasticsearch-7.3.1+-orange.svg)](https://www.elastic.co/)

## 🚀 项目简介

ESClientRHL 是一个基于 Elasticsearch 7.x 的 Java 客户端工具库，提供了简化的 API 来操作 Elasticsearch。

### ✨ 主要特性

- **简化的API设计** - 直观易用的ES操作方法
- **配置驱动** - 通过 EntityConfig 进行灵活配置
- **类型安全** - 完整的泛型支持
- **功能完整** - 支持索引管理、CRUD、查询、聚合
- **Spring集成** - 无缝集成 Spring Boot
- **纯API模式** - 支持独立使用

## 📦 安装

```xml
<dependency>
    <groupId>cn.zxporz</groupId>
    <artifactId>esclientrhl</artifactId>
    <version>7.0.2</version>
</dependency>
```

## 🏃 快速开始

### 1. 创建客户端

```java
ESClientFactory factory = new ESClientFactory("127.0.0.1:9200");
ElasticsearchTemplate<User, String> template = factory.getElasticsearchTemplate();
ElasticsearchIndex<User> indexManager = factory.getElasticsearchIndex();
```

### 2. 定义实体

```java
public class User {
    private String id;
    private String name;
    private Integer age;
    private String email;
    // getter/setter...
}
```

### 3. 配置映射

```java
MetaDataConfig metaData = new MetaDataConfig("user_index")
    .setNumberOfShards(1)
    .setNumberOfReplicas(0);

EntityConfig userConfig = new EntityConfig(User.class)
    .setMetaData(metaData)
    .setIdField("id")
    .addFieldMapping("name", new MappingConfig()
        .setDatatype(DataType.text_type)
        .setKeyword(true))
    .addFieldMapping("age", new MappingConfig()
        .setDatatype(DataType.integer_type));
```

### 4. 基本操作

```java
// 创建索引
indexManager.createIndex(userConfig);

// 保存文档
User user = new User();
user.setId("001");
user.setName("张三");
template.save(user, userConfig);

// 查询文档
User result = template.getById("001", userConfig);

// 搜索文档
List<User> users = template.search(QueryBuilders.matchAllQuery(), userConfig);

// 删除文档
template.delete("001", userConfig);

// 关闭客户端
factory.close();
```

## 📚 核心API

### ElasticsearchTemplate 主要方法

```java
// CRUD操作
boolean save(T entity, EntityConfig config)
T getById(M id, EntityConfig config)
boolean update(T entity, EntityConfig config)
boolean delete(M id, EntityConfig config)

// 查询操作
List<T> search(QueryBuilder query, EntityConfig config)
PageList<T> search(QueryBuilder query, PageSortHighLight page, EntityConfig config)
long count(QueryBuilder query, EntityConfig config)
```

### ElasticsearchIndex 主要方法

```java
// 索引管理
boolean createIndex(EntityConfig config)
boolean deleteIndex(EntityConfig config)
boolean exists(EntityConfig config)
```

## 🔧 配置选项

### 数据类型

- `DataType.text_type` - 文本类型
- `DataType.keyword_type` - 关键字类型
- `DataType.integer_type` - 整数类型
- `DataType.date_type` - 日期类型
- `DataType.boolean_type` - 布尔类型

### 分析器

- `Analyzer.standard` - 标准分析器
- `Analyzer.ik_max_word` - IK最大词分析器
- `Analyzer.ik_smart` - IK智能分析器

## 🧪 测试

```bash
# 运行简化测试（不需要ES服务器）
mvn test -Dtest=ESClientRHLSimpleTest

# 运行完整测试（需要ES服务器）
mvn test -Dtest=ESClientRHLTest
```

## 📖 文档

- [完整使用文档](ESClientRHL-使用文档.md) - 详细的API参考和示例
- [原版README](README.md) - 完整的项目说明

## 🔍 查询示例

### 基础查询

```java
// 匹配所有
template.search(QueryBuilders.matchAllQuery(), userConfig);

// 词条查询
template.search(QueryBuilders.termQuery("age", 25), userConfig);

// 范围查询
template.search(QueryBuilders.rangeQuery("age").gte(18).lte(65), userConfig);

// 文本匹配
template.search(QueryBuilders.matchQuery("name", "张三"), userConfig);
```

### 复杂查询

```java
BoolQueryBuilder boolQuery = QueryBuilders.boolQuery()
    .must(QueryBuilders.rangeQuery("age").gte(18))
    .must(QueryBuilders.termQuery("status", "ACTIVE"))
    .should(QueryBuilders.matchQuery("name", "张"))
    .mustNot(QueryBuilders.termQuery("deleted", true));

List<User> results = template.search(boolQuery, userConfig);
```

### 分页查询

```java
PageSortHighLight pageSort = new PageSortHighLight(1, 10); // 第1页，每页10条
PageList<User> pageResult = template.search(
    QueryBuilders.matchAllQuery(), pageSort, userConfig);

System.out.println("总数: " + pageResult.getTotalElements());
System.out.println("总页数: " + pageResult.getTotalPages());
```

## ⚠️ 注意事项

1. **版本兼容性**: 确保使用 Elasticsearch 7.3.1+
2. **连接管理**: 使用完毕后记得关闭 ESClientFactory
3. **配置验证**: 创建 EntityConfig 后检查 `isValid()` 方法
4. **异常处理**: 所有操作都可能抛出异常，需要适当处理

## 🐛 常见问题

### 连接失败
```java
// 检查ES服务是否启动
// 确认连接地址正确
ESClientFactory factory = new ESClientFactory("127.0.0.1:9200");
```

### 索引创建失败
```java
// 检查索引是否已存在
if (!indexManager.exists(userConfig)) {
    indexManager.createIndex(userConfig);
}
```

### 查询无结果
```java
// 等待ES刷新
Thread.sleep(1000);
// 或者手动刷新索引
```

## 📝 更新日志

### v7.0.2 (2024-01-01)
- ✅ 修复pom.xml标签错误
- ✅ 完善EntityConfig配置
- ✅ 添加完整测试用例
- ✅ 优化文档和示例

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

Apache License 2.0

## 👥 作者

**X-Pacific** - [zxporz](https://gitee.com/zxporz)

---

⭐ 如果对您有帮助，请给个星标！
