package org.zxp.esclientrhl;

import org.junit.Test;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.config.MappingConfig;
import org.zxp.esclientrhl.config.MetaDataConfig;
import org.zxp.esclientrhl.enums.Analyzer;
import org.zxp.esclientrhl.enums.DataType;

import java.util.Arrays;
import java.util.Date;

/**
 * ESClientRHL 简化测试类
 * 
 * 专注于测试核心功能，不依赖ES服务器
 * 主要测试配置和工厂类的基本功能
 * 
 * <AUTHOR> Test
 * @since 2024-01-01
 */
public class ESClientRHLSimpleTest {
    
    @Test
    public void testESClientFactoryCreation() {
        System.out.println("=== 测试ESClientFactory创建 ===");
        
        try {
            // 测试工厂类创建
            ESClientFactory factory = new ESClientFactory("127.0.0.1:9200");
            
            // 验证基本组件
            if (factory.getElasticsearchTemplate() != null) {
                System.out.println("✓ ElasticsearchTemplate 创建成功");
            }
            
            if (factory.getElasticsearchIndex() != null) {
                System.out.println("✓ ElasticsearchIndex 创建成功");
            }
            
            if (factory.getIndexTools() != null) {
                System.out.println("✓ IndexTools 创建成功");
            }
            
            if (factory.getClient() != null) {
                System.out.println("✓ RestHighLevelClient 创建成功");
            }
            
            // 关闭工厂
            factory.close();
            System.out.println("✓ ESClientFactory 关闭成功");
            
        } catch (Exception e) {
            System.err.println("✗ ESClientFactory 创建失败: " + e.getMessage());
            // 不抛出异常，因为可能ES服务器未启动
        }
        
        System.out.println("=== ESClientFactory创建测试完成 ===\n");
    }
    
    @Test
    public void testEntityConfigCreation() {
        System.out.println("=== 测试EntityConfig配置 ===");
        
        try {
            // 创建元数据配置
            MetaDataConfig metaData = new MetaDataConfig("test_index")
                .setNumberOfShards(1)
                .setNumberOfReplicas(0)
                .setPrintLog(true)
                .setAutoCreateIndex(true);
            
            System.out.println("✓ MetaDataConfig 创建成功");
            System.out.println("  - 索引名称: " + metaData.getIndexName());
            System.out.println("  - 分片数量: " + metaData.getNumberOfShards());
            System.out.println("  - 副本数量: " + metaData.getNumberOfReplicas());
            
            // 创建实体配置
            EntityConfig entityConfig = new EntityConfig(User.class)
                .setMetaData(metaData)
                .setIdField("id");
            
            // 添加字段映射
            entityConfig
                .addFieldMapping("name", new MappingConfig()
                    .setDatatype(DataType.text_type)
                    .setAnalyzer(Analyzer.standard)
                    .setKeyword(true))
                .addFieldMapping("age", new MappingConfig()
                    .setDatatype(DataType.integer_type))
                .addFieldMapping("email", new MappingConfig()
                    .setDatatype(DataType.keyword_type));
            
            System.out.println("✓ EntityConfig 创建成功");
            System.out.println("  - 实体类: " + entityConfig.getEntityClass().getSimpleName());
            System.out.println("  - ID字段: " + entityConfig.getIdField());
            System.out.println("  - 字段映射数量: " + entityConfig.getFieldMappings().size());
            
            // 验证配置
            if (entityConfig.isValid()) {
                System.out.println("✓ EntityConfig 配置验证通过");
            } else {
                System.out.println("✗ EntityConfig 配置验证失败");
            }
            
            // 测试字段映射
            if (entityConfig.hasFieldMapping("name")) {
                MappingConfig nameMapping = entityConfig.getFieldMapping("name");
                System.out.println("✓ name字段映射: " + nameMapping.getDatatype());
            }
            
            if (entityConfig.hasFieldMapping("age")) {
                MappingConfig ageMapping = entityConfig.getFieldMapping("age");
                System.out.println("✓ age字段映射: " + ageMapping.getDatatype());
            }
            
        } catch (Exception e) {
            System.err.println("✗ EntityConfig 配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== EntityConfig配置测试完成 ===\n");
    }
    
    @Test
    public void testMappingConfigCreation() {
        System.out.println("=== 测试MappingConfig配置 ===");
        
        try {
            // 测试文本字段映射
            MappingConfig textMapping = new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.standard)
                .setKeyword(true)
                .setNgram(true);
            
            System.out.println("✓ 文本字段映射创建成功");
            System.out.println("  - 数据类型: " + textMapping.getDatatype());
            System.out.println("  - 分析器: " + textMapping.getAnalyzer());
            System.out.println("  - 支持keyword: " + textMapping.isKeyword());
            System.out.println("  - 支持ngram: " + textMapping.isNgram());
            
            // 测试数值字段映射
            MappingConfig numberMapping = new MappingConfig()
                .setDatatype(DataType.integer_type);
            
            System.out.println("✓ 数值字段映射创建成功");
            System.out.println("  - 数据类型: " + numberMapping.getDatatype());
            
            // 测试日期字段映射
            MappingConfig dateMapping = new MappingConfig()
                .setDatatype(DataType.date_type)
                .setDateFormat(Arrays.asList("yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"));
            
            System.out.println("✓ 日期字段映射创建成功");
            System.out.println("  - 数据类型: " + dateMapping.getDatatype());
            System.out.println("  - 日期格式: " + dateMapping.getDateFormat());
            
            // 测试关键字段映射
            MappingConfig keywordMapping = new MappingConfig()
                .setDatatype(DataType.keyword_type)
                .setNormalizer("lowercase");
            
            System.out.println("✓ 关键字段映射创建成功");
            System.out.println("  - 数据类型: " + keywordMapping.getDatatype());
            System.out.println("  - 标准化器: " + keywordMapping.getNormalizer());
            
        } catch (Exception e) {
            System.err.println("✗ MappingConfig 配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== MappingConfig配置测试完成 ===\n");
    }
    
    @Test
    public void testUserEntityCreation() {
        System.out.println("=== 测试User实体创建 ===");
        
        try {
            // 创建用户实体
            User user = new User();
            user.setId("test001");
            user.setName("测试用户");
            user.setAge(25);
            user.setEmail("<EMAIL>");
            user.setDescription("这是一个测试用户");
            user.setCreateTime(new Date());
            user.setActive(true);
            user.setScore(85.5);
            user.setStatus("ACTIVE");
            
            System.out.println("✓ User实体创建成功");
            System.out.println("  - ID: " + user.getId());
            System.out.println("  - 姓名: " + user.getName());
            System.out.println("  - 年龄: " + user.getAge());
            System.out.println("  - 邮箱: " + user.getEmail());
            System.out.println("  - 描述: " + user.getDescription());
            System.out.println("  - 创建时间: " + user.getCreateTime());
            System.out.println("  - 是否激活: " + user.getActive());
            System.out.println("  - 评分: " + user.getScore());
            System.out.println("  - 状态: " + user.getStatus());
            
            // 测试toString方法
            System.out.println("✓ User.toString(): " + user.toString());
            
            // 测试equals和hashCode
            User user2 = new User();
            user2.setId("test001");
            
            if (user.equals(user2)) {
                System.out.println("✓ User.equals() 方法正常");
            }
            
            if (user.hashCode() == user2.hashCode()) {
                System.out.println("✓ User.hashCode() 方法正常");
            }
            
        } catch (Exception e) {
            System.err.println("✗ User实体创建失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== User实体创建测试完成 ===\n");
    }
    
    @Test
    public void testConfigurationChaining() {
        System.out.println("=== 测试配置链式调用 ===");
        
        try {
            // 测试链式配置
            EntityConfig config = new EntityConfig()
                .setMetaData(new MetaDataConfig("chain_test")
                    .setNumberOfShards(2)
                    .setNumberOfReplicas(1)
                    .setPrintLog(true))
                .setIdField("id")
                .setScoreField("score")
                .addFieldMapping("title", new MappingConfig()
                    .setDatatype(DataType.text_type)
                    .setAnalyzer(Analyzer.standard)
                    .setKeyword(true))
                .addFieldMapping("count", new MappingConfig()
                    .setDatatype(DataType.long_type));
            
            System.out.println("✓ 链式配置创建成功");
            System.out.println("  - 索引名称: " + config.getIndexName());
            System.out.println("  - ID字段: " + config.getIdField());
            System.out.println("  - 评分字段: " + config.getScoreField());
            System.out.println("  - 字段映射数量: " + config.getFieldMappings().size());
            
            // 验证配置完整性
            if (config.isValid() && config.hasIdField() && config.hasScoreField() && config.hasFieldMappings()) {
                System.out.println("✓ 链式配置验证通过");
            } else {
                System.out.println("✗ 链式配置验证失败");
            }
            
        } catch (Exception e) {
            System.err.println("✗ 链式配置失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 配置链式调用测试完成 ===\n");
    }
    
    @Test
    public void testAllComponents() {
        System.out.println("=== 综合功能测试 ===");
        
        try {
            System.out.println("开始综合测试...");
            
            // 1. 测试工厂创建
            testESClientFactoryCreation();
            
            // 2. 测试配置创建
            testEntityConfigCreation();
            
            // 3. 测试映射配置
            testMappingConfigCreation();
            
            // 4. 测试实体创建
            testUserEntityCreation();
            
            // 5. 测试链式配置
            testConfigurationChaining();
            
            System.out.println("✓ 所有组件测试通过！");
            
        } catch (Exception e) {
            System.err.println("✗ 综合测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=== 综合功能测试完成 ===");
    }
}
