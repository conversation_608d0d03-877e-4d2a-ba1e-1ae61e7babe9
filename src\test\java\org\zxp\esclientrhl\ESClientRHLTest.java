package org.zxp.esclientrhl;

import org.elasticsearch.index.query.QueryBuilders;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.config.MappingConfig;
import org.zxp.esclientrhl.config.MetaDataConfig;
import org.zxp.esclientrhl.enums.Analyzer;
import org.zxp.esclientrhl.enums.DataType;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;
import org.zxp.esclientrhl.util.IndexTools;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;

/**
 * ESClientRHL 完整功能测试类
 * 
 * 测试覆盖范围：
 * 1. ESClientFactory 工厂类功能
 * 2. ElasticsearchTemplate 模板操作
 * 3. ElasticsearchIndex 索引管理
 * 4. EntityConfig 配置方式
 * 5. 基础CRUD操作
 * 6. 查询和聚合功能
 * 7. 索引管理功能
 * 
 * <AUTHOR> Test
 * @since 2024-01-01
 */
public class ESClientRHLTest {
    
    private ESClientFactory factory;
    private ElasticsearchTemplate<User, String> template;
    private ElasticsearchIndex<User> indexManager;
    private IndexTools indexTools;
    private EntityConfig userConfig;
    
    @Before
    public void setUp() throws Exception {
        // 初始化ES客户端工厂（使用默认配置，假设ES运行在localhost:9200）
        factory = new ESClientFactory("127.0.0.1:9200");
        
        // 获取各种操作实例
        template = factory.getElasticsearchTemplate();
        indexManager = factory.getElasticsearchIndex();
        indexTools = factory.getIndexTools();
        
        // 创建用户实体配置
        userConfig = createUserEntityConfig();
        
        // 确保测试索引不存在（清理环境）
        try {
            indexManager.deleteIndex(userConfig);
        } catch (Exception e) {
            // 索引不存在，忽略异常
        }
    }
    
    @After
    public void tearDown() throws Exception {
        // 清理测试数据
        try {
            indexManager.deleteIndex(userConfig);
        } catch (Exception e) {
            // 忽略清理异常
        }
        
        // 关闭工厂
        if (factory != null) {
            factory.close();
        }
    }
    
    /**
     * 创建用户实体配置
     */
    private EntityConfig createUserEntityConfig() {
        MetaDataConfig metaData = new MetaDataConfig("user_test")
            .setNumberOfShards(1)
            .setNumberOfReplicas(0)
            .setPrintLog(true)
            .setAutoCreateIndex(true);
        
        EntityConfig entityConfig = new EntityConfig(User.class)
            .setMetaData(metaData)
            .setIdField("id");
        
        // 配置字段映射
        entityConfig
            .addFieldMapping("name", new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.standard)
                .setKeyword(true))
            .addFieldMapping("age", new MappingConfig()
                .setDatatype(DataType.integer_type))
            .addFieldMapping("email", new MappingConfig()
                .setDatatype(DataType.keyword_type))
            .addFieldMapping("description", new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.standard))
            .addFieldMapping("createTime", new MappingConfig()
                .setDatatype(DataType.date_type)
                .setDateFormat(Arrays.asList("yyyy-MM-dd HH:mm:ss")))
            .addFieldMapping("active", new MappingConfig()
                .setDatatype(DataType.boolean_type))
            .addFieldMapping("score", new MappingConfig()
                .setDatatype(DataType.double_type))
            .addFieldMapping("status", new MappingConfig()
                .setDatatype(DataType.keyword_type));
        
        return entityConfig;
    }
    
    /**
     * 创建测试用户
     */
    private User createTestUser(String id, String name, int age, String email) {
        User user = new User();
        user.setId(id);
        user.setName(name);
        user.setAge(age);
        user.setEmail(email);
        user.setDescription("测试用户描述：" + name);
        user.setCreateTime(new Date());
        user.setActive(true);
        user.setScore(Math.random() * 100);
        user.setStatus("ACTIVE");
        return user;
    }
    
    @Test
    public void testESClientFactory() {
        // 测试工厂类基本功能
        assertNotNull("ESClientFactory should not be null", factory);
        assertNotNull("ElasticsearchTemplate should not be null", template);
        assertNotNull("ElasticsearchIndex should not be null", indexManager);
        assertNotNull("IndexTools should not be null", indexTools);
        assertNotNull("RestHighLevelClient should not be null", factory.getClient());
        
        System.out.println("✓ ESClientFactory 基本功能测试通过");
    }
    
    @Test
    public void testEntityConfig() {
        // 测试实体配置
        assertNotNull("EntityConfig should not be null", userConfig);
        assertTrue("EntityConfig should be valid", userConfig.isValid());
        assertEquals("Index name should match", "user_test", userConfig.getIndexName());
        assertEquals("Index type should be _doc", "_doc", userConfig.getIndexType());
        assertTrue("Should have ID field", userConfig.hasIdField());
        assertEquals("ID field should be 'id'", "id", userConfig.getIdField());
        assertTrue("Should have field mappings", userConfig.hasFieldMappings());
        
        // 测试字段映射
        assertTrue("Should have name field mapping", userConfig.hasFieldMapping("name"));
        assertTrue("Should have age field mapping", userConfig.hasFieldMapping("age"));
        assertEquals("Name field should be text type", 
            DataType.text_type, userConfig.getFieldMapping("name").getDatatype());
        assertEquals("Age field should be integer type", 
            DataType.integer_type, userConfig.getFieldMapping("age").getDatatype());
        
        System.out.println("✓ EntityConfig 配置测试通过");
    }
    
    @Test
    public void testIndexManagement() throws Exception {
        // 测试索引管理功能
        
        // 1. 创建索引
        boolean created = indexManager.createIndex(userConfig);
        assertTrue("Index should be created successfully", created);
        
        // 2. 检查索引是否存在
        boolean exists = indexManager.exists(userConfig);
        assertTrue("Index should exist", exists);
        
        // 3. 删除索引
        boolean deleted = indexManager.deleteIndex(userConfig);
        assertTrue("Index should be deleted successfully", deleted);
        
        // 4. 再次检查索引是否存在
        boolean existsAfterDelete = indexManager.exists(userConfig);
        assertFalse("Index should not exist after deletion", existsAfterDelete);
        
        System.out.println("✓ 索引管理功能测试通过");
    }
    
    @Test
    public void testBasicCRUD() throws Exception {
        // 测试基础CRUD操作
        
        // 1. 创建索引
        indexManager.createIndex(userConfig);
        
        // 2. 保存文档
        User user = createTestUser("test001", "张三", 25, "<EMAIL>");
        boolean saved = template.save(user, userConfig);
        assertTrue("User should be saved successfully", saved);
        
        // 等待ES刷新
        Thread.sleep(1000);
        
        // 3. 根据ID查询
        User retrieved = template.getById("test001", userConfig);
        assertNotNull("Retrieved user should not be null", retrieved);
        assertEquals("User name should match", "张三", retrieved.getName());
        assertEquals("User age should match", Integer.valueOf(25), retrieved.getAge());
        
        // 4. 更新文档
        retrieved.setAge(26);
        retrieved.setDescription("更新后的描述");
        boolean updated = template.update(retrieved, userConfig);
        assertTrue("User should be updated successfully", updated);
        
        // 等待ES刷新
        Thread.sleep(1000);
        
        // 5. 验证更新
        User updatedUser = template.getById("test001", userConfig);
        assertEquals("Updated age should match", Integer.valueOf(26), updatedUser.getAge());
        assertEquals("Updated description should match", "更新后的描述", updatedUser.getDescription());
        
        // 6. 删除文档
        boolean deleted = template.delete("test001", userConfig);
        assertTrue("User should be deleted successfully", deleted);
        
        // 等待ES刷新
        Thread.sleep(1000);
        
        // 7. 验证删除
        User deletedUser = template.getById("test001", userConfig);
        assertNull("Deleted user should be null", deletedUser);
        
        System.out.println("✓ 基础CRUD操作测试通过");
    }
    
    @Test
    public void testBatchOperations() throws Exception {
        // 测试批量操作
        
        // 1. 创建索引
        indexManager.createIndex(userConfig);
        
        // 2. 批量保存
        List<User> users = Arrays.asList(
            createTestUser("batch001", "批量用户1", 20, "<EMAIL>"),
            createTestUser("batch002", "批量用户2", 25, "<EMAIL>"),
            createTestUser("batch003", "批量用户3", 30, "<EMAIL>")
        );
        
        // 批量保存（逐个保存，因为没有基于EntityConfig的批量保存方法）
        boolean batchSaved = true;
        for (User user : users) {
            batchSaved = batchSaved && template.save(user, userConfig);
        }
        assertTrue("Batch save should be successful", batchSaved);
        
        // 等待ES刷新
        Thread.sleep(2000);
        
        // 3. 验证批量保存
        List<User> searchResults = template.search(QueryBuilders.matchAllQuery(), userConfig);
        assertEquals("Should have 3 users", 3, searchResults.size());
        
        System.out.println("✓ 批量操作测试通过");
    }

    @Test
    public void testSearchOperations() throws Exception {
        // 测试搜索操作

        // 1. 创建索引并添加测试数据
        indexManager.createIndex(userConfig);

        List<User> users = Arrays.asList(
            createTestUser("search001", "搜索测试用户1", 20, "<EMAIL>"),
            createTestUser("search002", "搜索测试用户2", 25, "<EMAIL>"),
            createTestUser("search003", "测试用户3", 30, "<EMAIL>")
        );

        // 批量保存（逐个保存，因为没有基于EntityConfig的批量保存方法）
        for (User user : users) {
            template.save(user, userConfig);
        }
        Thread.sleep(2000); // 等待ES刷新

        // 2. 测试匹配所有查询
        List<User> allUsers = template.search(QueryBuilders.matchAllQuery(), userConfig);
        assertEquals("Should find all 3 users", 3, allUsers.size());

        // 3. 测试词条查询
        List<User> termResults = template.search(
            QueryBuilders.termQuery("age", 25), userConfig);
        assertEquals("Should find 1 user with age 25", 1, termResults.size());
        assertEquals("Found user should have age 25", Integer.valueOf(25), termResults.get(0).getAge());

        // 4. 测试范围查询
        List<User> rangeResults = template.search(
            QueryBuilders.rangeQuery("age").gte(20).lte(25), userConfig);
        assertEquals("Should find 2 users with age between 20-25", 2, rangeResults.size());

        // 5. 测试文本匹配查询
        List<User> matchResults = template.search(
            QueryBuilders.matchQuery("name", "搜索"), userConfig);
        assertEquals("Should find 2 users with '搜索' in name", 2, matchResults.size());

        System.out.println("✓ 搜索操作测试通过");
    }

    @Test
    public void testPaginationAndSorting() throws Exception {
        // 测试分页和排序

        // 1. 创建索引并添加测试数据
        indexManager.createIndex(userConfig);

        List<User> users = Arrays.asList(
            createTestUser("page001", "用户A", 30, "<EMAIL>"),
            createTestUser("page002", "用户B", 20, "<EMAIL>"),
            createTestUser("page003", "用户C", 25, "<EMAIL>"),
            createTestUser("page004", "用户D", 35, "<EMAIL>"),
            createTestUser("page005", "用户E", 22, "<EMAIL>")
        );

        template.save(users, userConfig);
        Thread.sleep(2000); // 等待ES刷新

        // 2. 测试分页查询
        PageSortHighLight pageSort = new PageSortHighLight(1, 2); // 第1页，每页2条
        PageList<User> pageResult = template.search(QueryBuilders.matchAllQuery(), pageSort, userConfig);

        assertNotNull("Page result should not be null", pageResult);
        assertEquals("Should have 2 items per page", 2, pageResult.getList().size());
        assertEquals("Total count should be 5", 5, pageResult.getTotalElements());
        assertTrue("Should have more pages", pageResult.getTotalPages() > 1);

        System.out.println("✓ 分页和排序测试通过");
    }

    @Test
    public void testAggregations() throws Exception {
        // 测试聚合查询

        // 1. 创建索引并添加测试数据
        indexManager.createIndex(userConfig);

        List<User> users = Arrays.asList(
            createTestUser("agg001", "聚合用户1", 20, "<EMAIL>"),
            createTestUser("agg002", "聚合用户2", 25, "<EMAIL>"),
            createTestUser("agg003", "聚合用户3", 20, "<EMAIL>"),
            createTestUser("agg004", "聚合用户4", 30, "<EMAIL>")
        );

        template.save(users, userConfig);
        Thread.sleep(2000); // 等待ES刷新

        // 2. 测试计数聚合
        long count = template.count(QueryBuilders.matchAllQuery(), userConfig);
        assertEquals("Should count 4 users", 4, count);

        // 3. 测试按年龄分组计数
        long countByAge = template.count(QueryBuilders.termQuery("age", 20), userConfig);
        assertEquals("Should count 2 users with age 20", 2, countByAge);

        System.out.println("✓ 聚合查询测试通过");
    }

    @Test
    public void testErrorHandling() throws Exception {
        // 测试错误处理

        // 1. 测试查询不存在的文档
        User nonExistentUser = template.getById("nonexistent", userConfig);
        assertNull("Non-existent user should be null", nonExistentUser);

        // 2. 测试删除不存在的文档
        boolean deleteResult = template.delete("nonexistent", userConfig);
        assertFalse("Delete non-existent document should return false", deleteResult);

        // 3. 测试无效的EntityConfig
        EntityConfig invalidConfig = new EntityConfig();
        assertFalse("Invalid config should not be valid", invalidConfig.isValid());

        System.out.println("✓ 错误处理测试通过");
    }

    @Test
    public void testConfigurationFlexibility() {
        // 测试配置灵活性

        // 1. 测试EntityConfig的链式配置
        EntityConfig config = new EntityConfig()
            .setMetaData(new MetaDataConfig("test_index"))
            .setIdField("id")
            .setScoreField("score")
            .addFieldMapping("name", new MappingConfig().setDatatype(DataType.text_type))
            .addFieldMapping("age", new MappingConfig().setDatatype(DataType.integer_type));

        assertTrue("Config should be valid", config.isValid());
        assertEquals("Index name should match", "test_index", config.getIndexName());
        assertTrue("Should have ID field", config.hasIdField());
        assertTrue("Should have score field", config.hasScoreField());
        assertTrue("Should have field mappings", config.hasFieldMappings());

        // 2. 测试MappingConfig的配置
        MappingConfig mapping = new MappingConfig()
            .setDatatype(DataType.text_type)
            .setAnalyzer(Analyzer.standard)
            .setKeyword(true)
            .setNgram(true);

        assertEquals("Datatype should match", DataType.text_type, mapping.getDatatype());
        assertEquals("Analyzer should match", Analyzer.standard, mapping.getAnalyzer());
        assertTrue("Should have keyword", mapping.isKeyword());
        assertTrue("Should have ngram", mapping.isNgram());

        System.out.println("✓ 配置灵活性测试通过");
    }

    @Test
    public void testPerformanceAndStability() throws Exception {
        // 测试性能和稳定性

        // 1. 创建索引
        indexManager.createIndex(userConfig);

        // 2. 批量插入大量数据
        int batchSize = 100;
        for (int i = 0; i < batchSize; i++) {
            User user = createTestUser("perf" + i, "性能测试用户" + i, 20 + (i % 50), "perf" + i + "@test.com");
            template.save(user, userConfig);
        }

        Thread.sleep(3000); // 等待ES刷新

        // 3. 验证数据量
        long totalCount = template.count(QueryBuilders.matchAllQuery(), userConfig);
        assertEquals("Should have " + batchSize + " users", batchSize, totalCount);

        // 4. 测试查询性能
        long startTime = System.currentTimeMillis();
        List<User> results = template.search(QueryBuilders.matchAllQuery(), userConfig);
        long endTime = System.currentTimeMillis();

        assertTrue("Query should return results", results.size() > 0);
        System.out.println("查询 " + results.size() + " 条记录耗时: " + (endTime - startTime) + "ms");

        System.out.println("✓ 性能和稳定性测试通过");
    }
}
