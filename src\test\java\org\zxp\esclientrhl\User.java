package org.zxp.esclientrhl;

import java.util.Date;

/**
 * 测试用户实体类
 *
 * 用于测试ESClientRHL的各种功能
 * 包含常见的字段类型，使用EntityConfig方式配置ES映射
 *
 * 注意：此类不再使用注解配置，而是通过EntityConfig进行配置
 *
 * <AUTHOR> Test
 * @since 2024-01-01
 */
public class User {

    private String id;
    private String name;
    private Integer age;
    private String email;
    private String description;
    private Date createTime;
    private Boolean active;
    private Double score;
    private String status;
    
    // 构造函数
    public User() {}
    
    public User(String id, String name, Integer age, String email) {
        this.id = id;
        this.name = name;
        this.age = age;
        this.email = email;
        this.createTime = new Date();
        this.active = true;
        this.score = 0.0;
        this.status = "ACTIVE";
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getAge() {
        return age;
    }
    
    public void setAge(Integer age) {
        this.age = age;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Date getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
    
    public Boolean getActive() {
        return active;
    }
    
    public void setActive(Boolean active) {
        this.active = active;
    }
    
    public Double getScore() {
        return score;
    }
    
    public void setScore(Double score) {
        this.score = score;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", age=" + age +
                ", email='" + email + '\'' +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                ", active=" + active +
                ", score=" + score +
                ", status='" + status + '\'' +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        User user = (User) o;
        
        return id != null ? id.equals(user.id) : user.id == null;
    }
    
    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
